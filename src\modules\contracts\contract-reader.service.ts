import { backofficeClient } from '../../shared/database/connection';
import { getQueue, QUEUE_NAMES } from '../../shared/queue/connection';
import { 
  ContractReaderJobData, 
  NotificationProcessorJobData, 
  JOB_NAMES,
  ContractExpirationNotificationType,
  ContractExpirationData
} from '../../shared/queue/types';
import logger from '../../shared/logger/logger';
import { addDays, differenceInDays, isAfter, isBefore } from 'date-fns';

// ============================================================================
// CONTRACT READER SERVICE
// ============================================================================

export class ContractReaderService {
  private readonly contractReaderQueue = getQueue(QUEUE_NAMES.CONTRACT_READER);
  private readonly notificationQueue = getQueue(QUEUE_NAMES.NOTIFICATION_PROCESSOR);

  /**
   * Processes contract reading job
   */
  async processContractReading(jobData: ContractReaderJobData): Promise<void> {
    const {
      batchSize = 100,
      daysBeforeExpiration = 30,
      lastProcessedId
    } = jobData;

    logger.info('Starting contract expiration check', {
      batchSize,
      daysBeforeExpiration,
      lastProcessedId
    });

    try {
      const expiringContracts = await this.findExpiringContracts({
        batchSize,
        daysBeforeExpiration,
        lastProcessedId
      });

      logger.info(`Found ${expiringContracts.length} expiring contracts`);

      // Process each contract and create notification jobs
      for (const contract of expiringContracts) {
        await this.createNotificationJob(contract);
      }

      // If we got a full batch, there might be more contracts to process
      if (expiringContracts.length === batchSize) {
        const lastContract = expiringContracts[expiringContracts.length - 1];
        await this.scheduleNextBatch({
          batchSize,
          daysBeforeExpiration,
          lastProcessedId: lastContract.id
        });
      }

      logger.info('Contract expiration check completed successfully');
    } catch (error) {
      logger.error('Error processing contract reading job', { error, jobData });
      throw error;
    }
  }

  /**
   * Finds contracts that are expiring within the specified timeframe
   */
  private async findExpiringContracts({
    batchSize,
    daysBeforeExpiration,
    lastProcessedId
  }: {
    batchSize: number;
    daysBeforeExpiration: number;
    lastProcessedId?: string;
  }) {
    const now = new Date();
    const expirationThreshold = addDays(now, daysBeforeExpiration);

    const whereClause: any = {
      status: 'ACTIVE', // Only check active contracts
      versions: {
        some: {
          expirationDate: {
            not: null,
            lte: expirationThreshold,
            gte: now // Not already expired
          }
        }
      }
    };

    // For pagination
    if (lastProcessedId) {
      whereClause.id = {
        gt: lastProcessedId
      };
    }

    const contracts = await backofficeClient.contract.findMany({
      where: whereClause,
      include: {
        versions: {
          where: {
            versionId: {
              equals: backofficeClient.contractVersion.fields.versionId
            }
          },
          orderBy: {
            versionId: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        id: 'asc'
      },
      take: batchSize
    });

    // Transform to our internal format
    return contracts.map(contract => {
      const latestVersion = contract.versions[0];
      const daysUntilExpiration = differenceInDays(
        latestVersion.expirationDate!,
        now
      );

      return {
        id: contract.id,
        contractId: contract.id,
        contractType: contract.contractType,
        entityType: contract.entityType,
        entityUuid: contract.entityUuid,
        expirationDate: latestVersion.expirationDate!,
        daysUntilExpiration,
        currentVersion: contract.currentVersion,
        filePath: latestVersion.filePath,
        observations: latestVersion.observations
      } as ContractExpirationData;
    });
  }

  /**
   * Creates a notification job for a contract
   */
  private async createNotificationJob(contract: ContractExpirationData): Promise<void> {
    const notificationType = this.determineNotificationType(contract.daysUntilExpiration);
    
    const jobData: NotificationProcessorJobData = {
      contractId: contract.contractId,
      contractType: contract.contractType,
      entityType: contract.entityType,
      entityUuid: contract.entityUuid,
      expirationDate: contract.expirationDate,
      currentVersion: contract.currentVersion,
      metadata: {
        notificationType,
        daysUntilExpiration: contract.daysUntilExpiration,
        filePath: contract.filePath,
        observations: contract.observations
      }
    };

    await this.notificationQueue.add(
      JOB_NAMES.PROCESS_CONTRACT_EXPIRATION,
      jobData,
      {
        priority: this.getJobPriority(contract.daysUntilExpiration),
        delay: 0
      }
    );

    logger.debug('Created notification job for contract', {
      contractId: contract.contractId,
      notificationType,
      daysUntilExpiration: contract.daysUntilExpiration
    });
  }

  /**
   * Determines the notification type based on days until expiration
   */
  private determineNotificationType(daysUntilExpiration: number): ContractExpirationNotificationType {
    if (daysUntilExpiration <= 0) {
      return ContractExpirationNotificationType.EXPIRED;
    } else if (daysUntilExpiration <= 7) {
      return ContractExpirationNotificationType.EXPIRING_SOON;
    } else {
      return ContractExpirationNotificationType.RENEWAL_REMINDER;
    }
  }

  /**
   * Gets job priority based on urgency
   */
  private getJobPriority(daysUntilExpiration: number): number {
    if (daysUntilExpiration <= 0) return 1; // Highest priority for expired
    if (daysUntilExpiration <= 7) return 2; // High priority for expiring soon
    if (daysUntilExpiration <= 15) return 3; // Medium priority
    return 4; // Lower priority for early reminders
  }

  /**
   * Schedules the next batch processing job
   */
  private async scheduleNextBatch(jobData: ContractReaderJobData): Promise<void> {
    await this.contractReaderQueue.add(
      JOB_NAMES.READ_EXPIRING_CONTRACTS,
      jobData,
      {
        delay: 1000, // Small delay to prevent overwhelming the system
        priority: 10
      }
    );
  }

  /**
   * Manually trigger contract reading (for testing or manual execution)
   */
  async triggerContractReading(options?: Partial<ContractReaderJobData>): Promise<void> {
    const jobData: ContractReaderJobData = {
      batchSize: 100,
      daysBeforeExpiration: 30,
      ...options
    };

    await this.contractReaderQueue.add(
      JOB_NAMES.READ_EXPIRING_CONTRACTS,
      jobData
    );

    logger.info('Contract reading job triggered manually', { jobData });
  }
}
